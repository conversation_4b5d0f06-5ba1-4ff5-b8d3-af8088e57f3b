# SSA-TCN-GRU 电动汽车充电负荷预测

实现了一个基于SSA-TCN-GRU（麻雀搜索算法优化的时间卷积网络-门控循环单元）的电动汽车充电负荷预测系统。能够预测充电站的每小时充电负荷，并使用麻雀搜索算法进行超参数优化。

## 环境要求

- Python 3.10.16
- PyTorch 2.0.1
- CUDA 11.8（如果使用GPU）
- pandas
- numpy
- scikit-learn
- tqdm


## 数据格式

输入数据应为CSV格式，包含以下字段：
- Capacity (kWh)
- Charging Station ID
- Charging Station
- Location
- start_time
- end_time
- historical_load
- duration
- Charging Rate (kW)
- chargeing_cost
- Time of Day
- Day of Week
- State of Charge (Start %)
- State of Charge (End %)
- Distance Driven (since last charge) (km)
- Temperature

## 使用方法

1. 准备数据：
   - 将您的充电数据保存为CSV格式
   - 确保数据包含所有必需字段
   - 将文件命名为`ev_charging_data.csv`并放在项目根目录

2. 运行训练：
```bash
python train.py
```

训练过程将：
- 自动处理和清洗数据
- 使用麻雀搜索算法优化模型超参数
- 训练模型并保存最佳模型
- 输出评估指标（MSE、RMSE、MAE）

## 项目结构

- `data_preprocessing.py`: 数据预处理模块
- `model.py`: SSA-TCN-GRU模型定义
- `sparrow_search.py`: 麻雀搜索算法实现
- `train.py`: 训练和评估脚本
- `requirements.txt`: 项目依赖
- `README.md`: 项目说明文档

## 模型架构

该模型结合了以下组件：
1. TCN（时间卷积网络）：捕获时间序列的长期依赖关系
2. GRU（门控循环单元）：处理序列数据并学习时间动态特征
3. SSA（麻雀搜索算法）：优化模型超参数

## 注意事项

1. 数据预处理：
   - 缺失值将使用相邻值的平均值填充
   - 连续缺失将使用前后一周相同时间段的均值填充
   - 数据将按7:1:2的比例划分为训练集、验证集和测试集

2. 硬件要求：
   - 推荐使用GPU进行训练
   - 如果使用CPU，训练时间可能会显著增加

3. 模型保存：
   - 最佳模型将保存为`best_model.pth`
   - 训练日志将实时输出到控制台

