
# 模型部署指南

## 1. 模型文件说明
- `final_best_model.pth`: 最终的最佳模型权重文件
- `final_model_config.json`: 模型配置和元数据

## 2. 加载模型
```python
from deploy_model_example import ModelDeployer

# 初始化部署器
deployer = ModelDeployer('path/to/final_model_config.json')

# 进行预测
predictions = deployer.predict(input_tensor)
```

## 3. 输入数据格式
- 形状: [batch_size, sequence_length, input_size]
- 数据类型: torch.FloatTensor
- 设备: 自动适配 CPU/GPU

## 4. 预处理要求
输入数据需要经过与训练时相同的预处理步骤：
1. 数据清洗和填充
2. 特征工程（时间特征、气象特征、滞后特征等）
3. 特征缩放和选择
4. 序列化处理

## 5. 性能指标
基于十折交叉验证的最佳模型：
- 平均MAPE: ~5.90%
- 平均R2: ~0.9492
- 最佳折MAPE: 1.38%

## 6. 注意事项
- 确保输入数据的特征维度与训练时一致
- 模型已针对小时级充电负荷预测优化
- 建议定期重新训练以适应数据分布变化
