import torch
import torch.nn as nn

class TemporalBlock(nn.Module):
    def __init__(self, n_inputs, n_outputs, kernel_size, stride, dilation, dropout=0.3):
        super(TemporalBlock, self).__init__()
        # 计算padding以保持序列长度
        padding = (kernel_size - 1) * dilation
        
        self.conv1 = nn.Conv1d(
            n_inputs, n_outputs, kernel_size,
            stride=stride, padding=padding, dilation=dilation
        )
        self.bn1 = nn.BatchNorm1d(n_outputs)
        self.relu1 = nn.ReLU()
        self.dropout1 = nn.Dropout(dropout)
        
        self.conv2 = nn.Conv1d(
            n_outputs, n_outputs, kernel_size,
            stride=stride, padding=padding, dilation=dilation
        )
        self.bn2 = nn.BatchNorm1d(n_outputs)
        self.relu2 = nn.ReLU()
        self.dropout2 = nn.Dropout(dropout)
        
        self.downsample = nn.Conv1d(n_inputs, n_outputs, 1) if n_inputs != n_outputs else None
        self.relu = nn.ReLU()
        
        # 保存padding值用于forward
        self.padding = padding
    
    def init_weights(self):
        """初始化权重"""
        for m in self.modules():
            if isinstance(m, nn.Conv1d):
                nn.init.kaiming_normal_(m.weight.data)
                if m.bias is not None:
                    nn.init.constant_(m.bias.data, 0)
            elif isinstance(m, nn.BatchNorm1d):
                nn.init.constant_(m.weight.data, 1)
                nn.init.constant_(m.bias.data, 0)
    
    def forward(self, x):
        # 保存输入用于残差连接
        res = x if self.downsample is None else self.downsample(x)
        
        # 第一个卷积层
        out = self.conv1(x)
        out = self.bn1(out)
        out = self.relu1(out)
        out = self.dropout1(out)
        
        # 第二个卷积层
        out = self.conv2(out)
        out = self.bn2(out)
        out = self.dropout2(out)
        
        # 确保残差连接和输出具有相同的序列长度
        if out.size(-1) != res.size(-1):
            # 如果输出序列长度大于残差连接，截断输出
            if out.size(-1) > res.size(-1):
                out = out[:, :, :res.size(-1)]
            # 如果输出序列长度小于残差连接，截断残差连接
            else:
                res = res[:, :, :out.size(-1)]
        
        # 残差连接
        out = out + res
        return self.relu(out)

class TCN(nn.Module):
    def __init__(self, input_size, num_channels, kernel_size=2, dropout=0.3):
        super(TCN, self).__init__()
        layers = []
        num_levels = len(num_channels)
        for i in range(num_levels):
            dilation_size = 2 ** i
            in_channels = input_size if i == 0 else num_channels[i-1]
            out_channels = num_channels[i]
            layers += [TemporalBlock(
                in_channels, out_channels, kernel_size,
                stride=1, dilation=dilation_size,
                dropout=dropout
            )]
        
        self.network = nn.Sequential(*layers)
    
    def forward(self, x):
        return self.network(x)

class Attention(nn.Module):
    def __init__(self, hidden_size):
        super(Attention, self).__init__()
        self.attention = nn.Sequential(
            nn.Linear(hidden_size, hidden_size // 2),
            nn.Tanh(),
            nn.Linear(hidden_size // 2, 1)
        )

    def forward(self, x):
        # x shape: (batch_size, seq_len, hidden_size)
        attn_weights = self.attention(x).squeeze(-1)
        # attn_weights shape: (batch_size, seq_len)
        soft_attn_weights = torch.softmax(attn_weights, dim=1)
        # context shape: (batch_size, hidden_size)
        context = torch.bmm(soft_attn_weights.unsqueeze(1), x).squeeze(1)
        return context, soft_attn_weights

class SSATCNGRU(nn.Module):
    def __init__(self, input_size, hidden_size, num_layers, output_size, kernel_size=3, dropout=0.3, gru_layers=2, l2_reg=1e-4):
        super(SSATCNGRU, self).__init__()
        self.input_size = input_size
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        self.output_size = output_size
        self.kernel_size = kernel_size
        self.l2_reg = l2_reg
        
        # TCN层
        num_channels = [hidden_size] * num_layers
        self.tcn = TCN(
            input_size=self.input_size,
            num_channels=num_channels,
            kernel_size=self.kernel_size,
            dropout=dropout
        )
        
        # GRU层
        self.gru = nn.GRU(
            input_size=hidden_size,
            hidden_size=hidden_size,
            num_layers=gru_layers,
            batch_first=True,
            dropout=dropout if gru_layers > 1 else 0,
            bidirectional=True # 使用双向GRU
        )
        
        # 注意力层
        self.attention = Attention(hidden_size * 2) # 因为是双向GRU，所以是hidden_size*2
        
        # 输出层
        self.fc = nn.Linear(hidden_size * 2, output_size) # 线性层输入维度也需要调整
        
        # 初始化权重
        self.init_weights()
    
    def init_weights(self):
        """初始化权重"""
        for name, param in self.named_parameters():
            if 'weight' in name:
                if len(param.shape) >= 2:
                    # 对于2维及以上的权重使用xavier初始化
                    nn.init.xavier_normal_(param)
                else:
                    # 对于1维权重使用正态分布初始化
                    nn.init.normal_(param, mean=0, std=0.01)
            elif 'bias' in name:
                nn.init.constant_(param, 0)
    
    def forward(self, x):
        # 输入形状: [batch_size, sequence_length, input_size]
        # 转换为: [batch_size, input_size, sequence_length]
        x = x.transpose(1, 2)
        
        # TCN层
        tcn_out = self.tcn(x)
        
        # 转换回: [batch_size, sequence_length, hidden_size]
        tcn_out = tcn_out.transpose(1, 2)
        
        # GRU层
        gru_out, _ = self.gru(tcn_out)
        
        # 注意力机制
        context, attn_weights = self.attention(gru_out)
        
        # 使用注意力加权的上下文向量
        # out = gru_out[:, -1, :]
        
        # 输出层
        out = self.fc(context)
        return out

def create_model(config):
    """创建模型实例"""
    return SSATCNGRU(
        config['input_size'],
        config['hidden_size'],
        config['num_layers'],
        config['output_size'],
        config.get('kernel_size', 3),
        config.get('dropout', 0.3),
        config.get('gru_layers', 1),  # 减少GRU层数，配合双向
        config.get('l2_reg', 1e-4)
    ) 