# 电动汽车充电负荷预测系统使用指南

## 系统概述

本系统是一个基于深度学习的电动汽车充电负荷预测系统，采用SSATCNGRU（Self-Supervised Attention TCN-GRU）模型架构，结合丰富的特征工程和先进的训练策略。

## 环境配置

### 1. 系统要求
- Python 3.8+
- CUDA 11.0+ (可选，用于GPU加速)
- 内存: 8GB+
- 存储: 2GB+

### 2. 依赖安装
```bash
# 安装基础依赖
pip install torch pandas numpy scikit-learn matplotlib tqdm

# 安装优化相关依赖
pip install scikit-optimize seaborn

# 可选：安装高级优化器
pip install torch-optimizer
```

### 3. 验证安装
```bash
python test_improved_system.py
```

## 数据格式要求

### 1. 数据文件格式
- 文件格式：CSV
- 编码：UTF-8 或 GBK
- 时间频率：小时级数据

### 2. 必需列名（中文）
```
充电时间          - 时间戳，格式：YYYY/MM/DD HH:MM
降水量(mm)        - 降水量，数值型
平均气温(℃)       - 平均气温，数值型
最低气温(℃)       - 最低气温，数值型
最高气温(℃)       - 最高气温，数值型
总有功功率_总和(kW) - 充电负荷（目标变量），数值型
```

### 3. 数据示例
```csv
充电时间,降水量(mm),平均气温(℃),最低气温(℃),最高气温(℃),总有功功率_总和(kW)
2024/7/2 0:00,13.2,25.9,23.7,30,0
2024/7/2 1:00,13.2,25.9,23.7,30,0
2024/7/2 2:00,13.2,25.9,23.7,30,0
```

## 快速开始

### 1. 基础使用
```python
from train import EVChargingPredictor

# 初始化预测器
predictor = EVChargingPredictor(device='cuda')  # 或 'cpu'

# 准备数据
train_loader, val_loader, test_loader, scaler = predictor.prepare_data('ev_charging_data.csv')

# 查看数据信息
print(f"输入特征维度: {predictor.input_size}")
print(f"训练集批次数: {len(train_loader)}")
```

### 2. 完整训练流程
```bash
# 运行完整训练（包含超参数优化和交叉验证）
python train.py
```

### 3. 快速训练测试
```bash
# 运行快速训练测试（3个epoch）
python quick_train_test.py
```

## 高级使用

### 1. 自定义数据预处理
```python
from data_preprocessing import DataPreprocessor

# 创建预处理器
preprocessor = DataPreprocessor(
    scaler_type='standard',  # 或 'minmax'
    k_best_features=20       # 特征选择数量
)

# 加载和处理数据
df = preprocessor.load_and_clean_data('your_data.csv')
df = preprocessor.fill_missing_values(df)

# 拟合特征缩放器
preprocessor.fit_features(df)

# 准备特征
processed_df = preprocessor.prepare_features(df, sequence_length=24)
```

### 2. 自定义模型配置
```python
from model import create_model

config = {
    'input_size': 10,        # 输入特征维度
    'hidden_size': 128,      # 隐藏层大小
    'num_layers': 3,         # TCN层数
    'output_size': 1,        # 输出维度
    'kernel_size': 3,        # 卷积核大小
    'dropout': 0.3,          # Dropout率
    'gru_layers': 2,         # GRU层数
    'l2_reg': 1e-4          # L2正则化
}

model = create_model(config)
```

### 3. 自定义训练参数
```python
# 修改超参数搜索空间
from skopt.space import Real, Integer, Categorical

space = [
    Integer(1, 5, name='num_layers'),
    Integer(64, 256, name='hidden_size'),
    Real(0.1, 0.5, name='dropout'),
    Real(1e-4, 1e-2, 'log-uniform', name='learning_rate')
]
```

## 输出文件说明

### 1. 训练输出目录结构
```
outputs/
└── YYYYMMDD_HHMMSS/
    ├── best_params.json           # 最优超参数
    ├── best_model_*.pth          # 最佳模型权重
    ├── training_history_*.png    # 训练历史图
    ├── feature_importance_*.png  # 特征重要性图
    ├── test_evaluation_*.png     # 测试评估图
    ├── prediction_results_*.csv  # 预测结果
    ├── evaluation_metrics_*.csv  # 评估指标
    └── cross_validation_results_*.csv  # 交叉验证结果
```

### 2. 关键文件说明

#### best_params.json
```json
{
    "num_layers": 3,
    "hidden_size": 128,
    "kernel_size": 5,
    "dropout": 0.25,
    "learning_rate": 0.001,
    "loss_type": "MSELoss",
    "optimizer_type": "Adam",
    "weight_decay": 0.0001
}
```

#### evaluation_metrics.csv
```csv
指标,值
MSE,12.2454
RMSE,3.4993
MAE,2.0622
MAPE(%),19.25
Accuracy(%),80.75
R2,0.8234
```

## 性能调优

### 1. 特征工程调优
```python
# 调整特征选择数量
preprocessor = DataPreprocessor(k_best_features=15)  # 增加特征数量

# 调整序列长度
processed_df = preprocessor.prepare_features(df, sequence_length=48)  # 使用更长序列
```

### 2. 模型调优
```python
# 增加模型复杂度
config = {
    'hidden_size': 256,      # 增大隐藏层
    'num_layers': 4,         # 增加层数
    'gru_layers': 3,         # 增加GRU层数
    'dropout': 0.2           # 降低dropout
}
```

### 3. 训练调优
```python
# 调整训练参数
predictor = EVChargingPredictor(
    sequence_length=48,      # 更长的序列
    device='cuda'           # 使用GPU加速
)
```

## 常见问题

### 1. 内存不足
```python
# 减小批次大小
train_loader = DataLoader(dataset, batch_size=32, ...)  # 默认64

# 减少特征数量
preprocessor = DataPreprocessor(k_best_features=10)
```

### 2. 训练速度慢
```python
# 使用GPU
device = 'cuda' if torch.cuda.is_available() else 'cpu'

# 减少超参数搜索次数
res = gp_minimize(objective, space, n_calls=20)  # 默认50
```

### 3. 预测精度低
```python
# 增加特征数量
preprocessor = DataPreprocessor(k_best_features=25)

# 增加模型复杂度
config['hidden_size'] = 256
config['num_layers'] = 4

# 增加训练轮数
# 在train.py中修改：for epoch in range(600):  # 默认400
```

## 监控和调试

### 1. 训练监控
```python
# 查看训练日志
logging.basicConfig(level=logging.INFO)

# 监控GPU使用
nvidia-smi
```

### 2. 结果分析
```python
# 查看特征重要性
importance_df = predictor.calculate_feature_importance(model, val_loader)
print(importance_df.head(10))

# 分析预测误差
errors = predictions - actuals
print(f"误差分布: 均值={errors.mean():.4f}, 标准差={errors.std():.4f}")
```

## 扩展开发

### 1. 添加新特征
```python
def create_custom_features(self, df):
    """添加自定义特征"""
    df['custom_feature'] = df['Temperature'] * df['Humidity']
    return df

# 在DataPreprocessor中添加此方法
```

### 2. 自定义损失函数
```python
class CustomLoss(nn.Module):
    def forward(self, pred, target):
        mse = nn.MSELoss()(pred, target)
        mae = nn.L1Loss()(pred, target)
        return mse + 0.1 * mae
```

### 3. 模型集成
```python
# 训练多个模型并集成
models = []
for i in range(5):
    model = create_model(config)
    # 训练模型...
    models.append(model)

# 集成预测
ensemble_pred = torch.mean(torch.stack([m(x) for m in models]), dim=0)
```

## 技术支持

如遇到问题，请检查：
1. 数据格式是否正确
2. 依赖包是否完整安装
3. 设备内存是否充足
4. CUDA版本是否兼容

更多技术细节请参考 `项目改进总结.md`。
